{"name": "daily-expo-demo", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android --device", "ios": "expo run:ios --device", "web": "expo start --web"}, "dependencies": {"@daily-co/config-plugin-rn-daily-js": "0.0.9", "@daily-co/react-native-daily-js": "^0.77.0", "@daily-co/react-native-webrtc": "118.0.3-daily.3", "@react-native-async-storage/async-storage": "1.24.0", "expo": "^53.0.10", "expo-build-properties": "~0.14.6", "expo-dev-client": "~5.2.0", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.3", "react-native-background-timer": "^2.4.1", "react-native-get-random-values": "^1.11.0"}, "devDependencies": {"@babel/core": "^7.27.4"}, "private": true}