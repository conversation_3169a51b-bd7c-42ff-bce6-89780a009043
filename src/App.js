import {
  View,
  SafeAreaView,
  StyleSheet,
  Text,
  Button,
  TextInput,
} from "react-native";
import React, { useEffect, useState, useCallback } from "react";
import Daily, {
  DailyMediaView,
  DailyEventObjectParticipant,
} from "@daily-co/react-native-daily-js";

const ROOM_URL_TEMPLATE = "https://vitalcare.daily.co/rHkUOS2Oy5HTfeWn39QZ";

export default function App() {
  const [inCall, setInCall] = useState(false);
  const [roomUrl, setRoomUrl] = useState("");
  const [videoTrack, setVideoTrack] = useState(null); // local track
  const [remoteVideoTrack, setRemoteVideoTrack] = useState(null); // remote track

  const handleNewParticipantsState = (event) => {
    const participant = event.participant;
    // Early out as needed to avoid display the local participant's video
    if (participant.local) {
        const videoTrack = participant.tracks.video;
    setRemotevideoTrack(videoTrack.persistentTrack);
      return;
    }
    const videoTrack = participant.tracks.video;
    setVideoTrack(videoTrack.persistentTrack);
    // Set participant count minus the local participant
    setRemoteParticipantCount(callObject.participantCounts().present - 1);
  };

  const joinRoom = async () => {
    console.log("Joining room");
    try {
      await callObject.join({
        url: roomUrl,
      });
      setInCall(true);
    } catch (e) {
      console.log("Received error when joining:", e);
    }
  };

  const leaveRoom = async () => {
    console.log("Leaving the room");
    await callObject.leave();
     setInCall(false);
    setVideoTrack(null);
    setRemoteVideoTrack(null);
  };

  // Create the callObject and join the meeting
  useEffect(() => {
    const callObject = Daily.createCallObject();
    setCallObject(callObject);
    return () => {};
  }, []);

  //Add the listeners
  useEffect(() => {
    if (!callObject) {
      return;
    }
    callObject
      .on("joined-meeting", () => setInCall(true))
      .on("left-meeting", () => setInCall(false))
      .on("participant-joined", handleNewParticipantsState)
      .on("participant-updated", handleNewParticipantsState)
      .on("participant-left", handleNewParticipantsState);
    return () => {};
  }, [callObject]);

 return (
    <SafeAreaView style={styles.safeArea}>
      {inCall ? (
        <View style={styles.inCallContainer}>
          <View style={styles.videoWrapper}>
            <DailyMediaView
              videoTrack={videoTrack}
              mirror={false}
              objectFit="cover"
              style={styles.dailyMediaView}
            />
          </View>
          <View style={styles.videoWrapper}>
            <DailyMediaView
              videoTrack={remoteVideoTrack}
              mirror={false}
              objectFit="cover"
              style={styles.dailyMediaView}
            />
          </View>
          <Button
            onPress={leaveRoom}
            title="Leave call"
            color="#d9534f"
          />
        </View>
      ) : (
        <View style={styles.outCallContainer}>
          <Text>Not in a call yet</Text>
          <TextInput
            style={styles.roomUrlInput}
            value={roomUrl}
            onChangeText={setRoomUrl}
            placeholder="Enter room URL"
          />
          <Button onPress={joinRoom} title="Join call" />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#000",
  },
  inCallContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  videoWrapper: {
    flex: 1, // half of screen
    width: "100%",
  },
  dailyMediaView: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  outCallContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  roomUrlInput: {
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 5,
    padding: 8,
    marginVertical: 10,
    width: "80%",
    backgroundColor: "#fff",
  },
});
