# Contributing

Thank you for looking into contributing to `daily-demos`! We want this repo to help people experiment with different Daily projects more quickly. We especially welcome any contributions that help us make existing demos easier to understand or that improve demos' instructions and descriptions, and we're especially excited about any new demos that highlight unique ways to use the [Daily API](https://docs.daily.co/reference).

**Before contributing:**

- [Run daily-demos locally](#run-daily-demos-locally)
- [Read our code of conduct](#read-our-code-of-conduct)

**How to contribute:**

- [Open or claim an issue](#open-or-claim-an-issue)
- [Open a pull request](#open-a-pull-request)
- [Contribute a new demo project](#contribute-a-new-demo-project)

## Before contributing

### Try to run the demo locally

Please follow the instructions in `README.md`.

### Read our code of conduct

We use the [Contributor Covenant](https://www.contributor-covenant.org/) for our Code of Conduct. Before contributing, [please read it](CODE_OF_CONDUCT.md).

## How to contribute

### Open or claim an issue

#### Open an issue

Today we work off two main issue templates: _bug reports_ and _demo/feature requests_.

_Bug reports_

Before creating a new bug report, please do two things:

1. If you want to report a bug you experienced while on a Daily call, try out these [troubleshooting tips](https://help.daily.co/en/articles/2303117-top-troubleshooting-tips) to see if that takes care of the bug.
2. If you're still seeing the error, check to see if somebody else has [already filed the issue](https://github.com/daily-demos/daily-expo-demo/issues) before creating a new one.

If you've done those two things and need to create an issue, we'll ask you to tell us:

- What you expected to happen
- What actually happened
- Steps to reproduce the error
- Screenshots that illustrate where and what we should be looking for when we reproduce
- System information, like your device, OS, library version, and if applicable, browser
- Any additional context that you think could help us work through this

_Demo/feature requests_

We're always happy to hear about new ways you'd like to use Daily. If you'd like a demo that we don't have yet, we'll ask you to let us know:

- If the demo will help you solve a particular problem
- Alternative solutions you've considered
- Any additional context that might help us understand this ask

#### Claim an issue

Any issues labeled `good-first-issue` are up for grabs. If you'd like to tackle an existing issue, feel free to assign yourself, and please leave a comment letting everyone know that you're on it.

### Open a pull request

- If it's been a minute or if you haven't yet cloned, forked, or branched a repository, GitHub has some [docs to help](https://docs.github.com/en/github/collaborating-with-issues-and-pull-requests).
- When creating commit messages and pull request titles, please follow the [conventional commits](https://www.conventionalcommits.org/en/v1.0.0/) standard.

### Contribute a new demo project

If you've built a project on Daily that you want to share with other developers, we'd be more than happy to help spread the word.

To add a new demo project:

Open a PR in [awesome-daily](#) and add a link to your project.
