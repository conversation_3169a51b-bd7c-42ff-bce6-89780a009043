{"expo": {"name": "daily-expo-demo", "slug": "daily-expo-demo", "version": "1.0.0", "newArchEnabled": true, "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bitcode": false, "bundleIdentifier": "co.daily.expo.DailyDemo", "infoPlist": {"UIBackgroundModes": ["voip"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "co.daily.expo.DailyDemo", "permissions": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.BLUETOOTH", "android.permission.CAMERA", "android.permission.INTERNET", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.RECORD_AUDIO", "android.permission.SYSTEM_ALERT_WINDOW", "android.permission.WAKE_LOCK", "android.permission.FOREGROUND_SERVICE", "android.permission.FOREGROUND_SERVICE_CAMERA", "android.permission.FOREGROUND_SERVICE_MICROPHONE", "android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION", "android.permission.POST_NOTIFICATIONS"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["@daily-co/config-plugin-rn-daily-js", {"enableCamera": true, "enableMicrophone": true, "enableScreenShare": false}], ["expo-build-properties", {"android": {"minSdkVersion": 24, "compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0"}, "ios": {"deploymentTarget": "15.1"}}]]}}